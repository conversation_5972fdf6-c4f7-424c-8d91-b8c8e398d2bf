"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Edit, Trash2, Search, Filter, Calendar, CheckCircle, Clock, AlertCircle, User } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface Task {
  id: string
  title: string
  description: string
  assignedTo: string
  assignedToName: string
  priority: "Low" | "Medium" | "High"
  status: "Todo" | "In Progress" | "Completed"
  dueDate: string
  createdAt: string
  avatar?: string
}

const mockEmployees = [
  { id: "EMP001", name: "John Doe", avatar: "/images/avatar.png" },
  { id: "EMP002", name: "Jane Smith", avatar: "/images/avatar.png" },
  { id: "EMP003", name: "Mike Johnson", avatar: "/images/avatar.png" },
  { id: "EMP004", name: "Sarah Wilson", avatar: "/images/avatar.png" },
]

const mockTaskData: Task[] = [
  {
    id: "1",
    title: "Update Customer Database",
    description: "Review and update customer information in the CRM system",
    assignedTo: "EMP001",
    assignedToName: "John Doe",
    priority: "High",
    status: "In Progress",
    dueDate: "2024-01-20",
    createdAt: "2024-01-15",
    avatar: "/images/avatar.png",
  },
  {
    id: "2",
    title: "Prepare Monthly Report",
    description: "Compile and analyze monthly sales and performance data",
    assignedTo: "EMP002",
    assignedToName: "Jane Smith",
    priority: "Medium",
    status: "Todo",
    dueDate: "2024-01-25",
    createdAt: "2024-01-16",
    avatar: "/images/avatar.png",
  },
  {
    id: "3",
    title: "Client Presentation",
    description: "Create presentation for upcoming client meeting",
    assignedTo: "EMP003",
    assignedToName: "Mike Johnson",
    priority: "High",
    status: "Completed",
    dueDate: "2024-01-18",
    createdAt: "2024-01-14",
    avatar: "/images/avatar.png",
  },
]

export default function AdminTasksPage() {
  const [tasks, setTasks] = useState<Task[]>(mockTaskData)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    assignedTo: "",
    priority: "Medium" as "Low" | "Medium" | "High",
    dueDate: "",
    status: "Todo" as "Todo" | "In Progress" | "Completed",
  })

  const filteredTasks = tasks.filter((task) => {
    const matchesSearch =
      task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.assignedToName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || task.status === statusFilter
    const matchesPriority = priorityFilter === "all" || task.priority === priorityFilter
    return matchesSearch && matchesStatus && matchesPriority
  })

  const handleCreateTask = () => {
    setEditingTask(null)
    setFormData({
      title: "",
      description: "",
      assignedTo: "",
      priority: "Medium",
      dueDate: "",
      status: "Todo",
    })
    setDialogOpen(true)
  }

  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    setFormData({
      title: task.title,
      description: task.description,
      assignedTo: task.assignedTo,
      priority: task.priority,
      dueDate: task.dueDate,
      status: task.status,
    })
    setDialogOpen(true)
  }

  const handleSaveTask = () => {
    const assignedEmployee = mockEmployees.find((emp) => emp.id === formData.assignedTo)

    if (editingTask) {
      // Update existing task
      setTasks((prev) =>
        prev.map((task) =>
          task.id === editingTask.id
            ? {
                ...task,
                title: formData.title,
                description: formData.description,
                assignedTo: formData.assignedTo,
                assignedToName: assignedEmployee?.name || "",
                priority: formData.priority,
                dueDate: formData.dueDate,
                status: formData.status,
                avatar: assignedEmployee?.avatar,
              }
            : task,
        ),
      )
      toast({
        title: "Task Updated",
        description: "Task has been successfully updated",
      })
    } else {
      // Create new task
      const newTask: Task = {
        id: Date.now().toString(),
        title: formData.title,
        description: formData.description,
        assignedTo: formData.assignedTo,
        assignedToName: assignedEmployee?.name || "",
        priority: formData.priority,
        status: formData.status,
        dueDate: formData.dueDate,
        createdAt: new Date().toISOString().split("T")[0],
        avatar: assignedEmployee?.avatar,
      }
      setTasks((prev) => [...prev, newTask])
      toast({
        title: "Task Created",
        description: "New task has been successfully assigned",
      })
    }

    setDialogOpen(false)
  }

  const handleDeleteTask = (id: string) => {
    setTasks((prev) => prev.filter((task) => task.id !== id))
    toast({
      title: "Task Deleted",
      description: "Task has been successfully deleted",
    })
  }

  const handleStatusChange = (id: string, newStatus: "Todo" | "In Progress" | "Completed") => {
    setTasks((prev) => prev.map((task) => (task.id === id ? { ...task, status: newStatus } : task)))
    toast({
      title: "Status Updated",
      description: `Task status changed to ${newStatus}`,
    })
  }

  const getPriorityBadge = (priority: string) => {
    const variants = {
      Low: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      Medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      High: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
    }
    return variants[priority as keyof typeof variants] || variants.Medium
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      Todo: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
      "In Progress": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      Completed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    }
    return variants[status as keyof typeof variants] || variants.Todo
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Todo":
        return <Clock className="h-4 w-4" />
      case "In Progress":
        return <AlertCircle className="h-4 w-4" />
      case "Completed":
        return <CheckCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const stats = {
    total: tasks.length,
    todo: tasks.filter((t) => t.status === "Todo").length,
    inProgress: tasks.filter((t) => t.status === "In Progress").length,
    completed: tasks.filter((t) => t.status === "Completed").length,
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Task Management</h1>
          <p className="text-muted-foreground">Assign and manage tasks for your team members</p>
        </div>
        <Button onClick={handleCreateTask}>
          <Plus className="mr-2 h-4 w-4" />
          Assign Task
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">To Do</CardTitle>
            <Clock className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.todo}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <AlertCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tasks Table */}
      <Card>
        <CardHeader>
          <CardTitle>Task Assignments</CardTitle>
          <CardDescription>Manage and track task assignments for your team</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tasks or employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Todo">To Do</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Task</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTasks.map((task) => (
                <TableRow key={task.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{task.title}</div>
                      <div className="text-sm text-muted-foreground line-clamp-2">{task.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={task.avatar || "/placeholder.svg"} />
                        <AvatarFallback>{task.assignedToName.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{task.assignedToName}</div>
                        <div className="text-sm text-muted-foreground">{task.assignedTo}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPriorityBadge(task.priority)}>{task.priority}</Badge>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={task.status}
                      onValueChange={(value: "Todo" | "In Progress" | "Completed") =>
                        handleStatusChange(task.id, value)
                      }
                    >
                      <SelectTrigger className="w-[140px]">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(task.status)}
                          <SelectValue />
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Todo">To Do</SelectItem>
                        <SelectItem value="In Progress">In Progress</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline" onClick={() => handleEditTask(task)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteTask(task.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Create/Edit Task Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{editingTask ? "Edit Task" : "Assign New Task"}</DialogTitle>
            <DialogDescription>
              {editingTask ? "Update task details and assignment" : "Create and assign a new task to an employee"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Task Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
                placeholder="Enter task title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                placeholder="Enter task description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="assignedTo">Assign To</Label>
                <Select
                  value={formData.assignedTo}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, assignedTo: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select employee" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockEmployees.map((employee) => (
                      <SelectItem key={employee.id} value={employee.id}>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4" />
                          <span>{employee.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value: "Low" | "Medium" | "High") =>
                    setFormData((prev) => ({ ...prev, priority: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData((prev) => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: "Todo" | "In Progress" | "Completed") =>
                    setFormData((prev) => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Todo">To Do</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveTask}>{editingTask ? "Update Task" : "Assign Task"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
