"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { KanbanBoard } from "@/components/kanban-board"
import { HighPriorityReminders } from "@/components/high-priority-reminders"
import { FeatureDescription } from "@/components/feature-description"
import { ChatbotButton } from "@/components/chatbot-button"
import { useAuth } from "@/components/auth-provider"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { Calendar, Clock, Users, TrendingUp, ArrowRight, Plus } from "lucide-react"
import { TaskModal } from "@/components/task-modal"
import { useRouter } from "next/navigation"

// Mock data for tasks
const initialTasks = [
  {
    id: "task1",
    title: "Complete financial report",
    description: "Finish Q2 financial report for client review",
    status: "todo",
    userId: "JD",
    priority: "high",
  },
  {
    id: "task2",
    title: "Client meeting preparation",
    description: "Prepare slides and talking points for client meeting",
    status: "inprogress",
    userId: "JS",
    priority: "medium",
  },
  {
    id: "task3",
    title: "Update CRM data",
    description: "Update client information in the CRM system",
    status: "done",
    userId: "AJ",
    priority: "low",
  },
  {
    id: "task4",
    title: "Review marketing strategy",
    description: "Review and provide feedback on the new marketing strategy",
    status: "todo",
    userId: "JD",
    priority: "high",
  },
  {
    id: "task5",
    title: "Prepare training materials",
    description: "Create training materials for new team members",
    status: "inprogress",
    userId: "AJ",
    priority: "medium",
  },
]

export default function Dashboard() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("tasks")
  const [tasks, setTasks] = useState(initialTasks)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingTask, setEditingTask] = useState(null)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>
  }

  const handleAddTask = () => {
    setEditingTask(null)
    setIsModalOpen(true)
  }

  const handleEditTask = (task) => {
    setEditingTask(task)
    setIsModalOpen(true)
  }

  const handleDeleteTask = (taskId) => {
    setTasks(tasks.filter((task) => task.id !== taskId))
  }

  const handleUpdateTaskStatus = (taskId: string, newStatus: string) => {
    setTasks(tasks.map((task) => (task.id === taskId ? { ...task, status: newStatus } : task)))
  }

  const handleSaveTask = (taskData) => {
    if (editingTask) {
      // Update existing task
      setTasks(tasks.map((task) => (task.id === editingTask.id ? { ...task, ...taskData } : task)))
    } else {
      // Add new task
      const newTask = {
        id: `task${tasks.length + 1}`,
        ...taskData,
        status: "todo",
      }
      setTasks([...tasks, newTask])
    }
    setIsModalOpen(false)
  }

  const handleTaskClick = (taskId) => {
    const task = tasks.find((t) => t.id === taskId)
    if (task) {
      setEditingTask(task)
      setIsModalOpen(true)
    }
  }

  const isAdmin = user?.role === "admin"
  const currentUserId = user?.id || ""

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20 md:pb-6">
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-teal-800 dark:text-teal-300">Welcome, {user?.full_name || user?.email || "User"}</h1>
            <p className="text-gray-600 dark:text-gray-400">Here's what's happening today</p>
          </div>
          <div className="flex items-center gap-2 mt-4 md:mt-0">
            <Button variant="outline" className="text-sm bg-transparent">
              <Calendar className="h-4 w-4 mr-2" />
              Today
            </Button>
            <Button className="bg-exobank-green hover:bg-exobank-green/90 text-white text-sm">
              <Clock className="h-4 w-4 mr-2" />
              Check In
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Total Leads</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">127</span>
                  <span className="text-sm text-green-600 dark:text-green-400">+12% from last month</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Active Campaigns</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">8</span>
                  <span className="text-sm text-green-600 dark:text-green-400">+2 new this week</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardContent className="p-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500 dark:text-gray-400">Conversion Rate</span>
                <div className="flex items-end justify-between mt-1">
                  <span className="text-2xl font-bold dark:text-white">24.5%</span>
                  <span className="text-sm text-red-600 dark:text-red-400">-3% from last month</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="tasks" className="mb-6" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
          </TabsList>

          <TabsContent value="tasks" className="mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              <div className="lg:col-span-3">
                <Card className="dark:bg-gray-800">
                  <CardHeader className="pb-2 flex flex-row items-center justify-between">
                    <div>
                      <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Task Management</CardTitle>
                      <CardDescription>Organize and track your marketing tasks</CardDescription>
                    </div>
                    <Button
                      size="sm"
                      onClick={handleAddTask}
                      className="bg-exobank-green hover:bg-exobank-green/90 text-white"
                    >
                      <Plus className="h-4 w-4 mr-1" /> Add Task
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <KanbanBoard
                      tasks={tasks}
                      onEditTask={handleEditTask}
                      onDeleteTask={handleDeleteTask}
                      onUpdateTaskStatus={handleUpdateTaskStatus}
                      isAdmin={isAdmin}
                      currentUserId={currentUserId}
                    />
                  </CardContent>
                </Card>
              </div>

              <div className="lg:col-span-1">
                <HighPriorityReminders tasks={tasks} onTaskClick={handleTaskClick} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <Card className="dark:bg-gray-800 lg:col-span-2">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Campaign Performance</CardTitle>
                  <CardDescription>Monthly performance metrics</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <LineChart
                    data={[
                      { name: "Jan", Leads: 65, Conversions: 28 },
                      { name: "Feb", Leads: 59, Conversions: 24 },
                      { name: "Mar", Leads: 80, Conversions: 35 },
                      { name: "Apr", Leads: 81, Conversions: 32 },
                      { name: "May", Leads: 56, Conversions: 20 },
                      { name: "Jun", Leads: 55, Conversions: 21 },
                    ]}
                    index="name"
                    categories={["Leads", "Conversions"]}
                    colors={["#00cc00", "#0088ff"]}
                    valueFormatter={(value) => `${value}`}
                    yAxisWidth={40}
                    className="h-72"
                  />
                </CardContent>
              </Card>

              <Card className="dark:bg-gray-800">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Lead Sources</CardTitle>
                  <CardDescription>Where your leads come from</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <BarChart
                    data={[
                      { name: "Website", value: 45 },
                      { name: "Social", value: 30 },
                      { name: "Email", value: 15 },
                      { name: "Referral", value: 10 },
                    ]}
                    index="name"
                    categories={["value"]}
                    colors={["#00cc00"]}
                    valueFormatter={(value) => `${value}%`}
                    yAxisWidth={40}
                    className="h-72"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="team" className="mt-4">
            <Card className="dark:bg-gray-800">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Team Members</CardTitle>
                <CardDescription>Your marketing team</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {[
                    { name: "John Doe", role: "Marketing Manager", tasks: 12 },
                    { name: "Jane Smith", role: "Content Writer", tasks: 8 },
                    { name: "Robert Johnson", role: "SEO Specialist", tasks: 10 },
                    { name: "Emily Davis", role: "Social Media Manager", tasks: 15 },
                    { name: "Michael Wilson", role: "Graphic Designer", tasks: 7 },
                    { name: "Sarah Brown", role: "Email Marketing", tasks: 9 },
                  ].map((member, index) => (
                    <Card key={index} className="dark:bg-gray-700">
                      <CardContent className="p-4">
                        <div className="flex flex-col items-center text-center">
                          <div className="w-16 h-16 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center mb-3">
                            <Users className="h-8 w-8 text-gray-500 dark:text-gray-400" />
                          </div>
                          <h3 className="font-medium dark:text-white">{member.name}</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{member.role}</p>
                          <div className="mt-2 flex items-center text-sm">
                            <TrendingUp className="h-4 w-4 mr-1 text-exobank-green" />
                            <span>{member.tasks} active tasks</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card className="dark:bg-gray-800 mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Featured Tools</CardTitle>
            <CardDescription>Explore our marketing tools</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <FeatureDescription
                title="Lead Management"
                description="Track and manage potential customers"
                icon="Users"
                link="/leads"
              />
              <FeatureDescription
                title="Campaign Builder"
                description="Create and manage marketing campaigns"
                icon="Megaphone"
                link="/campaigns"
              />
              <FeatureDescription
                title="Analytics Dashboard"
                description="Visualize your marketing performance"
                icon="BarChart"
                link="/analytics"
              />
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Recent Activities</CardTitle>
              <CardDescription>Latest updates from your team</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { user: "Jane Smith", action: "added a new lead", time: "2 hours ago" },
                  { user: "Robert Johnson", action: "completed task: Create social media posts", time: "3 hours ago" },
                  { user: "Emily Davis", action: "launched email campaign", time: "5 hours ago" },
                  { user: "Michael Wilson", action: "updated landing page design", time: "Yesterday" },
                ].map((activity, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center flex-shrink-0">
                      <Users className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div>
                      <p className="text-sm">
                        <span className="font-medium dark:text-white">{activity.user}</span>{" "}
                        <span className="text-gray-600 dark:text-gray-400">{activity.action}</span>
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
              <Button variant="ghost" className="w-full mt-4 text-exobank-green">
                View All Activities <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          <Card className="dark:bg-gray-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-teal-800 dark:text-teal-300">Upcoming Deadlines</CardTitle>
              <CardDescription>Tasks due soon</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { task: "Finalize Q3 Marketing Strategy", deadline: "Tomorrow", priority: "High" },
                  { task: "Review Social Media Analytics", deadline: "In 2 days", priority: "Medium" },
                  { task: "Prepare Monthly Report", deadline: "In 3 days", priority: "High" },
                  { task: "Update Customer Personas", deadline: "Next week", priority: "Low" },
                ].map((task, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium dark:text-white">{task.task}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Due: {task.deadline}</p>
                    </div>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        task.priority === "High"
                          ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
                          : task.priority === "Medium"
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
                            : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                      }`}
                    >
                      {task.priority}
                    </span>
                  </div>
                ))}
              </div>
              <Button variant="ghost" className="w-full mt-4 text-exobank-green">
                View All Tasks <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      <ChatbotButton />
      {isModalOpen && (
        <TaskModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSave={handleSaveTask}
          task={editingTask}
          currentUserId={currentUserId}
        />
      )}
    </div>
  )
}
