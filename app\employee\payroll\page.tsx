"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { useAuth } from "@/components/auth-provider"
import { DollarSign, Download, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AppHeader } from "@/components/app-header"

export default function PayrollPage() {
  const { user } = useAuth()

  const payrollHistory = [
    {
      period: "May 2023",
      payDate: "2023-05-31",
      grossPay: "$3,200.00",
      deductions: "$640.00",
      netPay: "$2,560.00",
      status: "Paid",
    },
    {
      period: "April 2023",
      payDate: "2023-04-30",
      grossPay: "$3,200.00",
      deductions: "$640.00",
      netPay: "$2,560.00",
      status: "Paid",
    },
    {
      period: "March 2023",
      payDate: "2023-03-31",
      grossPay: "$3,200.00",
      deductions: "$640.00",
      netPay: "$2,560.00",
      status: "Paid",
    },
  ]

  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
        <h1 className="text-2xl font-bold mb-6 text-foreground">Payroll</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="bg-card text-card-foreground">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <DollarSign className="mr-2" />
                Current Pay Period
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Period:</span>
                  <span className="font-medium text-foreground">May 1 - May 31, 2023</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Pay Date:</span>
                  <span className="font-medium text-foreground">May 31, 2023</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Status:</span>
                  <span className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 px-2 py-1 rounded text-sm">
                    Processing
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-card text-card-foreground">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Clock className="mr-2" />
                Hours Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Regular Hours:</span>
                  <span className="font-medium text-foreground">160 hrs</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Overtime Hours:</span>
                  <span className="font-medium text-foreground">8 hrs</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Total Hours:</span>
                  <span className="font-medium text-foreground">168 hrs</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="mb-8 bg-card text-card-foreground">
          <CardHeader>
            <CardTitle className="text-foreground">Current Pay Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-muted/50 p-4 rounded-lg">
                <p className="text-muted-foreground text-sm">Gross Pay</p>
                <p className="text-2xl font-bold text-foreground">$3,360.00</p>
              </div>
              <div className="bg-muted/50 p-4 rounded-lg">
                <p className="text-muted-foreground text-sm">Deductions</p>
                <p className="text-2xl font-bold text-foreground">$672.00</p>
              </div>
              <div className="bg-primary/10 p-4 rounded-lg">
                <p className="text-muted-foreground text-sm">Net Pay</p>
                <p className="text-2xl font-bold text-primary">$2,688.00</p>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="font-medium mb-2 text-foreground">Earnings Breakdown</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-foreground">Regular Pay (160 hrs × $20/hr)</span>
                  <span className="text-foreground">$3,200.00</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground">Overtime Pay (8 hrs × $20/hr × 1.5)</span>
                  <span className="text-foreground">$160.00</span>
                </div>
              </div>

              <h3 className="font-medium mb-2 mt-4 text-foreground">Deductions</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-foreground">Tax (15%)</span>
                  <span className="text-foreground">$504.00</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground">Insurance</span>
                  <span className="text-foreground">$120.00</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground">Retirement (401k)</span>
                  <span className="text-foreground">$48.00</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <h2 className="text-xl font-semibold mb-4 text-foreground">Payroll History</h2>
        <div className="overflow-x-auto bg-card rounded-lg border border-border">
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-foreground">Pay Period</TableHead>
                <TableHead className="text-foreground">Pay Date</TableHead>
                <TableHead className="text-foreground">Gross Pay</TableHead>
                <TableHead className="text-foreground">Deductions</TableHead>
                <TableHead className="text-foreground">Net Pay</TableHead>
                <TableHead className="text-foreground">Status</TableHead>
                <TableHead className="text-foreground">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payrollHistory.map((item, index) => (
                <TableRow key={index} className="border-border">
                  <TableCell className="text-foreground">{item.period}</TableCell>
                  <TableCell className="text-foreground">{item.payDate}</TableCell>
                  <TableCell className="text-foreground">{item.grossPay}</TableCell>
                  <TableCell className="text-foreground">{item.deductions}</TableCell>
                  <TableCell className="font-medium text-foreground">{item.netPay}</TableCell>
                  <TableCell>
                    <span className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 px-2 py-1 rounded text-sm">
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm" className="flex items-center text-foreground">
                      <Download className="h-4 w-4 mr-1" />
                      PDF
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
