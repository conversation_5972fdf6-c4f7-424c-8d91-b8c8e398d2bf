import bcrypt from "bcryptjs"
import { cookies } from "next/headers"
import { serverDb, generateSessionToken, type User } from "./server-db"

const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000 // 7 days

export interface AuthResult {
  success: boolean
  error?: string
  user?: User
  sessionToken?: string
}

export class AuthService {
  static async hashPassword(password: string): Promise<string> {
    try {
      if (!password) {
        throw new Error("Password is required")
      }

      return await bcrypt.hash(password, 12)
    } catch (error) {
      console.error("Error hashing password:", error)
      throw new Error("Failed to hash password")
    }
  }

  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      if (!password || !hash) {
        return false
      }

      return await bcrypt.compare(password, hash)
    } catch (error) {
      console.error("Error verifying password:", error)
      return false
    }
  }

  static async login(email: string, password: string): Promise<AuthResult> {
    try {
      if (!email || !password) {
        return { success: false, error: "Email and password are required" }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return { success: false, error: "Invalid email format" }
      }

      // Find user by email
      const user = await serverDb.getUserByEmail(email.toLowerCase().trim())
      if (!user) {
        return { success: false, error: "Invalid email or password" }
      }

      // Get password hash from database
      const result = await serverDb.sql`
        SELECT password_hash FROM users WHERE email = ${email.toLowerCase().trim()}
      `

      if (!result[0]) {
        return { success: false, error: "Invalid email or password" }
      }

      // Verify password
      const isValidPassword = await this.verifyPassword(password, result[0].password_hash)
      if (!isValidPassword) {
        return { success: false, error: "Invalid email or password" }
      }

      // Generate session token
      const sessionToken = generateSessionToken()
      const expiresAt = new Date(Date.now() + SESSION_DURATION)

      // Create session in database
      await serverDb.createSession(user.id, sessionToken, expiresAt)

      // Update last login
      await serverDb.updateUserLastLogin(user.id)

      return {
        success: true,
        user,
        sessionToken,
      }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, error: "An unexpected error occurred" }
    }
  }

  static async register(userData: {
    email: string
    password: string
    full_name: string
    role?: string
    department?: string
    position?: string
  }): Promise<AuthResult> {
    try {
      if (!userData.email || !userData.password || !userData.full_name) {
        return { success: false, error: "Email, password, and full name are required" }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        return { success: false, error: "Invalid email format" }
      }

      // Validate password strength
      if (userData.password.length < 6) {
        return { success: false, error: "Password must be at least 6 characters long" }
      }

      // Check if user already exists
      const existingUser = await serverDb.getUserByEmail(userData.email.toLowerCase().trim())
      if (existingUser) {
        return { success: false, error: "User already exists with this email" }
      }

      // Hash password
      const passwordHash = await this.hashPassword(userData.password)

      // Create user
      const user = await serverDb.createUser({
        email: userData.email.toLowerCase().trim(),
        password_hash: passwordHash,
        full_name: userData.full_name.trim(),
        role: (userData.role as any) || "staff",
        department: userData.department?.trim(),
        position: userData.position?.trim(),
        is_active: true,
        email_verified: false,
      })

      return {
        success: true,
        user,
      }
    } catch (error) {
      console.error("Registration error:", error)
      if (error.message?.includes("already exists")) {
        return { success: false, error: "User already exists with this email" }
      }
      return { success: false, error: "An unexpected error occurred" }
    }
  }

  static async verifySession(sessionToken: string): Promise<User | null> {
    try {
      if (!sessionToken) {
        return null
      }

      const session = await serverDb.getSessionByToken(sessionToken)
      if (!session) {
        return null
      }

      const user = await serverDb.getUserById(session.user_id)
      return user
    } catch (error) {
      console.error("Session verification error:", error)
      return null
    }
  }

  static async logout(sessionToken: string): Promise<void> {
    try {
      if (sessionToken) {
        await serverDb.deleteSession(sessionToken)
      }
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  static async getCurrentUser(): Promise<User | null> {
    try {
      const cookieStore = await cookies()
      const sessionToken = cookieStore.get("session-token")?.value

      if (!sessionToken) {
        return null
      }

      return this.verifySession(sessionToken)
    } catch (error) {
      console.error("Get current user error:", error)
      return null
    }
  }

  static async cleanup(): Promise<void> {
    try {
      await serverDb.deleteExpiredSessions()
    } catch (error) {
      console.error("Cleanup error:", error)
    }
  }
}
