// Server-side only database operations
import { neon } from "@neondatabase/serverless"
import { randomBytes } from "crypto"

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required")
}

const sql = neon(process.env.DATABASE_URL)

// Types for our database
export type UserRole = "admin" | "hr_manager" | "manager" | "staff"
export type AttendanceStatus = "present" | "absent" | "late" | "half_day" | "on_leave"
export type TaskStatus = "todo" | "in_progress" | "completed" | "cancelled"
export type TaskPriority = "low" | "medium" | "high" | "urgent"
export type PayrollStatus = "draft" | "processed" | "paid"

export interface User {
  id: string
  email: string
  full_name: string
  role: UserRole
  department?: string
  position?: string
  phone?: string
  hire_date?: string
  salary?: number
  is_active: boolean
  email_verified: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

export interface UserSession {
  id: string
  user_id: string
  session_token: string
  expires_at: string
  created_at: string
}

export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
}

// Simple session token generator
export function generateSessionToken(): string {
  return randomBytes(32).toString("hex")
}

// Server-side database operations
export const serverDb = {
  sql, // Export sql for direct queries if needed
  
  // User operations
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      if (!email) {
        throw new Error("Email is required")
      }

      const result = await sql`
        SELECT * FROM users WHERE email = ${email} AND is_active = true
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching user by email:", error)
      throw new Error("Failed to fetch user")
    }
  },

  async getUserById(id: string): Promise<User | null> {
    try {
      if (!id) {
        throw new Error("User ID is required")
      }

      const result = await sql`
        SELECT * FROM users WHERE id = ${id} AND is_active = true
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching user by ID:", error)
      throw new Error("Failed to fetch user")
    }
  },

  async createUser(
    userData: Omit<User, "id" | "created_at" | "updated_at"> & { password_hash: string },
  ): Promise<User> {
    try {
      if (!userData.email || !userData.password_hash || !userData.full_name) {
        throw new Error("Email, password hash, and full name are required")
      }

      const result = await sql`
        INSERT INTO users (
          email, password_hash, full_name, role, department, position, 
          phone, hire_date, salary, is_active, email_verified
        ) VALUES (
          ${userData.email}, ${userData.password_hash}, ${userData.full_name}, 
          ${userData.role}, ${userData.department || null}, ${userData.position || null},
          ${userData.phone || null}, ${userData.hire_date || null}, ${userData.salary || null},
          ${userData.is_active}, ${userData.email_verified}
        ) RETURNING *
      `
      return result[0]
    } catch (error) {
      console.error("Error creating user:", error)
      if (error.message?.includes("duplicate key")) {
        throw new Error("User with this email already exists")
      }
      throw new Error("Failed to create user")
    }
  },

  async updateUserLastLogin(userId: string): Promise<void> {
    try {
      if (!userId) {
        throw new Error("User ID is required")
      }

      await sql`
        UPDATE users SET last_login = NOW() WHERE id = ${userId}
      `
    } catch (error) {
      console.error("Error updating last login:", error)
      throw new Error("Failed to update last login")
    }
  },

  // Session operations
  async createSession(userId: string, sessionToken: string, expiresAt: Date): Promise<UserSession> {
    try {
      if (!userId || !sessionToken || !expiresAt) {
        throw new Error("User ID, session token, and expiration date are required")
      }

      const result = await sql`
        INSERT INTO user_sessions (user_id, session_token, expires_at)
        VALUES (${userId}, ${sessionToken}, ${expiresAt.toISOString()})
        RETURNING *
      `
      return result[0]
    } catch (error) {
      console.error("Error creating session:", error)
      throw new Error("Failed to create session")
    }
  },

  async getSessionByToken(sessionToken: string): Promise<UserSession | null> {
    try {
      if (!sessionToken) {
        return null
      }

      const result = await sql`
        SELECT * FROM user_sessions 
        WHERE session_token = ${sessionToken} AND expires_at > NOW()
      `
      return result[0] || null
    } catch (error) {
      console.error("Error fetching session:", error)
      return null
    }
  },

  async deleteSession(sessionToken: string): Promise<void> {
    try {
      if (!sessionToken) {
        return
      }

      await sql`
        DELETE FROM user_sessions WHERE session_token = ${sessionToken}
      `
    } catch (error) {
      console.error("Error deleting session:", error)
      // Don't throw as logout should still work
    }
  },

  async deleteExpiredSessions(): Promise<void> {
    try {
      await sql`
        DELETE FROM user_sessions WHERE expires_at <= NOW()
      `
    } catch (error) {
      console.error("Error deleting expired sessions:", error)
    }
  },

  // Permission operations
  async getUserPermissions(role: UserRole): Promise<Permission[]> {
    try {
      if (!role) {
        return []
      }

      const result = await sql`
        SELECT p.* FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role = ${role}
      `
      return result
    } catch (error) {
      console.error("Error fetching permissions:", error)
      return []
    }
  },

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await sql`SELECT 1`
      return true
    } catch (error) {
      console.error("Database health check failed:", error)
      return false
    }
  },
}
