"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/components/auth-provider"
import { Clock, CheckCircle, XCircle } from "lucide-react"
import { AppHeader } from "@/components/app-header"

export default function AttendancePage() {
  const { user } = useAuth()
  const [checkedIn, setCheckedIn] = useState(false)
  const [checkInTime, setCheckInTime] = useState<string | null>(null)
  const [checkOutTime, setCheckOutTime] = useState<string | null>(null)
  const [attendanceHistory, setAttendanceHistory] = useState([
    { date: "2023-05-15", checkIn: "09:00 AM", checkOut: "05:30 PM", hoursWorked: "8.5" },
    { date: "2023-05-14", checkIn: "08:45 AM", checkOut: "05:15 PM", hoursWorked: "8.5" },
    { date: "2023-05-13", checkIn: "09:15 AM", checkOut: "06:00 PM", hoursWorked: "8.75" },
  ])

  const handleCheckIn = () => {
    const now = new Date()
    const formattedTime = now.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    setCheckInTime(formattedTime)
    setCheckedIn(true)
  }

  const handleCheckOut = () => {
    const now = new Date()
    const formattedTime = now.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
    setCheckOutTime(formattedTime)
    setCheckedIn(false)

    // Add to history
    if (checkInTime) {
      const today = new Date().toISOString().split("T")[0]
      // Calculate hours worked (simplified)
      const hoursWorked = "8.0" // This would be calculated based on actual times

      setAttendanceHistory([
        { date: today, checkIn: checkInTime, checkOut: formattedTime, hoursWorked },
        ...attendanceHistory,
      ])

      setCheckInTime(null)
      setCheckOutTime(null)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
        <h1 className="text-2xl font-bold mb-6 text-foreground">Attendance</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="col-span-1 bg-card text-card-foreground">
            <CardHeader>
              <CardTitle className="flex items-center text-foreground">
                <Clock className="mr-2" />
                Today's Attendance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center space-y-4">
                <div className="text-center">
                  <p className="text-lg font-medium text-foreground">
                    {new Date().toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                  {checkInTime && <p className="text-sm text-muted-foreground">Check-in: {checkInTime}</p>}
                </div>

                <div className="flex flex-col sm:flex-row gap-4 w-full justify-center">
                  <Button onClick={handleCheckIn} disabled={checkedIn} className="flex items-center" size="lg">
                    <CheckCircle className="mr-2 h-5 w-5" />
                    Check In
                  </Button>
                  <Button
                    onClick={handleCheckOut}
                    disabled={!checkedIn}
                    variant="outline"
                    className="flex items-center border-border"
                    size="lg"
                  >
                    <XCircle className="mr-2 h-5 w-5" />
                    Check Out
                  </Button>
                </div>

                {checkedIn && (
                  <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-md text-center w-full">
                    <p className="text-green-700 dark:text-green-300">
                      You are currently checked in since {checkInTime}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1 bg-card text-card-foreground">
            <CardHeader>
              <CardTitle className="text-foreground">Attendance Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-foreground">Present Days (This Month):</span>
                  <span className="font-medium text-foreground">15</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground">Absent Days:</span>
                  <span className="font-medium text-foreground">0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground">Late Check-ins:</span>
                  <span className="font-medium text-foreground">2</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground">Early Check-outs:</span>
                  <span className="font-medium text-foreground">1</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground">Total Hours Worked:</span>
                  <span className="font-medium text-foreground">120.5</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <h2 className="text-xl font-semibold mt-8 mb-4 text-foreground">Attendance History</h2>
        <div className="overflow-x-auto bg-card rounded-lg border border-border">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-muted">
                <th className="p-3 text-left text-foreground">Date</th>
                <th className="p-3 text-left text-foreground">Check In</th>
                <th className="p-3 text-left text-foreground">Check Out</th>
                <th className="p-3 text-left text-foreground">Hours Worked</th>
              </tr>
            </thead>
            <tbody>
              {attendanceHistory.map((record, index) => (
                <tr key={index} className="border-b border-border">
                  <td className="p-3 text-foreground">{record.date}</td>
                  <td className="p-3 text-foreground">{record.checkIn}</td>
                  <td className="p-3 text-foreground">{record.checkOut}</td>
                  <td className="p-3 text-foreground">{record.hoursWorked}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
