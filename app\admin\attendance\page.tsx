"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Clock, Edit, UserCheck, UserX, Search, Filter, Calendar } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface AttendanceRecord {
  id: string
  employeeId: string
  employeeName: string
  date: string
  checkIn: string | null
  checkOut: string | null
  status: "Present" | "Absent" | "Late" | "On Break"
  hoursWorked: number
  avatar?: string
}

const mockAttendanceData: AttendanceRecord[] = [
  {
    id: "1",
    employeeId: "EMP001",
    employeeName: "John Doe",
    date: "2024-01-17",
    checkIn: "09:00",
    checkOut: "17:30",
    status: "Present",
    hoursWorked: 8.5,
    avatar: "/images/avatar.png",
  },
  {
    id: "2",
    employeeId: "EMP002",
    employeeName: "Jane Smith",
    date: "2024-01-17",
    checkIn: "09:15",
    checkOut: null,
    status: "Present",
    hoursWorked: 0,
    avatar: "/images/avatar.png",
  },
  {
    id: "3",
    employeeId: "EMP003",
    employeeName: "Mike Johnson",
    date: "2024-01-17",
    checkIn: "09:30",
    checkOut: "17:00",
    status: "Late",
    hoursWorked: 7.5,
    avatar: "/images/avatar.png",
  },
  {
    id: "4",
    employeeId: "EMP004",
    employeeName: "Sarah Wilson",
    date: "2024-01-17",
    checkIn: null,
    checkOut: null,
    status: "Absent",
    hoursWorked: 0,
    avatar: "/images/avatar.png",
  },
]

export default function AdminAttendancePage() {
  const [attendanceData, setAttendanceData] = useState<AttendanceRecord[]>(mockAttendanceData)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [selectedRecord, setSelectedRecord] = useState<AttendanceRecord | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [checkInTime, setCheckInTime] = useState("")
  const [checkOutTime, setCheckOutTime] = useState("")

  const filteredData = attendanceData.filter((record) => {
    const matchesSearch =
      record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || record.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleCheckIn = (employeeId: string) => {
    const currentTime = new Date().toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
    })

    setAttendanceData((prev) =>
      prev.map((record) =>
        record.employeeId === employeeId ? { ...record, checkIn: currentTime, status: "Present" as const } : record,
      ),
    )

    toast({
      title: "Check-in Successful",
      description: `Employee checked in at ${currentTime}`,
    })
  }

  const handleCheckOut = (employeeId: string) => {
    const currentTime = new Date().toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
    })

    setAttendanceData((prev) =>
      prev.map((record) => {
        if (record.employeeId === employeeId && record.checkIn) {
          const checkInTime = new Date(`2024-01-01 ${record.checkIn}`)
          const checkOutTime = new Date(`2024-01-01 ${currentTime}`)
          const hoursWorked = (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60 * 60)

          return {
            ...record,
            checkOut: currentTime,
            hoursWorked: Math.round(hoursWorked * 10) / 10,
          }
        }
        return record
      }),
    )

    toast({
      title: "Check-out Successful",
      description: `Employee checked out at ${currentTime}`,
    })
  }

  const handleEditAttendance = (record: AttendanceRecord) => {
    setSelectedRecord(record)
    setCheckInTime(record.checkIn || "")
    setCheckOutTime(record.checkOut || "")
    setEditDialogOpen(true)
  }

  const handleSaveEdit = () => {
    if (!selectedRecord) return

    let hoursWorked = 0
    if (checkInTime && checkOutTime) {
      const checkIn = new Date(`2024-01-01 ${checkInTime}`)
      const checkOut = new Date(`2024-01-01 ${checkOutTime}`)
      hoursWorked = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60)
    }

    setAttendanceData((prev) =>
      prev.map((record) =>
        record.id === selectedRecord.id
          ? {
              ...record,
              checkIn: checkInTime || null,
              checkOut: checkOutTime || null,
              hoursWorked: Math.round(hoursWorked * 10) / 10,
              status: checkInTime ? ("Present" as const) : ("Absent" as const),
            }
          : record,
      ),
    )

    setEditDialogOpen(false)
    toast({
      title: "Attendance Updated",
      description: "Employee attendance has been successfully updated",
    })
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      Present: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      Absent: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      Late: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      "On Break": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    }
    return variants[status as keyof typeof variants] || variants.Present
  }

  const stats = {
    present: attendanceData.filter((r) => r.status === "Present").length,
    absent: attendanceData.filter((r) => r.status === "Absent").length,
    late: attendanceData.filter((r) => r.status === "Late").length,
    total: attendanceData.length,
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Attendance Management</h1>
          <p className="text-muted-foreground">Manage employee attendance, check-ins, and working hours</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Today: {new Date().toLocaleDateString()}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Present</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.present}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Absent</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.absent}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Late</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.late}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Employee Attendance</CardTitle>
          <CardDescription>Track and manage employee attendance for today</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Present">Present</SelectItem>
                <SelectItem value="Absent">Absent</SelectItem>
                <SelectItem value="Late">Late</SelectItem>
                <SelectItem value="On Break">On Break</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Employee ID</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Check In</TableHead>
                <TableHead>Check Out</TableHead>
                <TableHead>Hours Worked</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={record.avatar || "/placeholder.svg"} />
                      <AvatarFallback>{record.employeeName.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{record.employeeName}</span>
                  </TableCell>
                  <TableCell>{record.employeeId}</TableCell>
                  <TableCell>
                    <Badge className={getStatusBadge(record.status)}>{record.status}</Badge>
                  </TableCell>
                  <TableCell>{record.checkIn || "-"}</TableCell>
                  <TableCell>{record.checkOut || "-"}</TableCell>
                  <TableCell>{record.hoursWorked > 0 ? `${record.hoursWorked}h` : "-"}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {!record.checkIn && (
                        <Button
                          size="sm"
                          onClick={() => handleCheckIn(record.employeeId)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Check In
                        </Button>
                      )}
                      {record.checkIn && !record.checkOut && (
                        <Button size="sm" variant="outline" onClick={() => handleCheckOut(record.employeeId)}>
                          Check Out
                        </Button>
                      )}
                      <Button size="sm" variant="outline" onClick={() => handleEditAttendance(record)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Attendance Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Attendance</DialogTitle>
            <DialogDescription>Update attendance times for {selectedRecord?.employeeName}</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="checkin" className="text-right">
                Check In
              </Label>
              <Input
                id="checkin"
                type="time"
                value={checkInTime}
                onChange={(e) => setCheckInTime(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="checkout" className="text-right">
                Check Out
              </Label>
              <Input
                id="checkout"
                type="time"
                value={checkOutTime}
                onChange={(e) => setCheckOutTime(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
