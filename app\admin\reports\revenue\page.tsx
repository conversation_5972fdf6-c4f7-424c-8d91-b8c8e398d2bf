"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>ead<PERSON> } from "@/components/app-header"
import { AdminSidebar } from "@/components/admin-sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from "@/components/ui/chart"
import { Download, Calendar, TrendingUp, TrendingDown, DollarSign, Users, CreditCard, Filter } from "lucide-react"

export default function RevenueReportsPage() {
  const [timeRange, setTimeRange] = useState("month")
  const [showFilters, setShowFilters] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <AppHeader />

      <div className="flex flex-1">
        <AdminSidebar />

        <div className="flex-1 p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
            <div>
              <h1 className="text-xl font-semibold text-teal-800 dark:text-teal-300">Revenue Reports</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">Financial performance and revenue analytics</p>
            </div>

            <div className="flex flex-wrap gap-2">
              <Select defaultValue="month" onValueChange={setTimeRange}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" size="icon" onClick={() => setShowFilters(!showFilters)}>
                <Filter className="h-4 w-4" />
              </Button>

              <Button variant="outline">
                <Calendar className="mr-2 h-4 w-4" />
                <span>Date Range</span>
              </Button>

              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                <span>Export</span>
              </Button>
            </div>
          </div>

          {showFilters && (
            <Card className="mb-6 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                      Department
                    </label>
                    <select className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm">
                      <option value="">All Departments</option>
                      <option value="marketing">Marketing</option>
                      <option value="sales">Sales</option>
                      <option value="finance">Finance</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                      Revenue Type
                    </label>
                    <select className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm">
                      <option value="">All Types</option>
                      <option value="product">Product Sales</option>
                      <option value="service">Services</option>
                      <option value="subscription">Subscriptions</option>
                    </select>
                  </div>

                  <div className="flex items-end">
                    <Button className="w-full bg-exobank-green hover:bg-exobank-green/90">Apply Filters</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Revenue</p>
                    <h3 className="text-2xl font-bold dark:text-white">₹4,235,890</h3>
                    <div className="flex items-center mt-1 text-green-600 text-xs">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      <span>15.3% from last period</span>
                    </div>
                  </div>
                  <div className="h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Average Transaction</p>
                    <h3 className="text-2xl font-bold dark:text-white">₹12,450</h3>
                    <div className="flex items-center mt-1 text-green-600 text-xs">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      <span>5.2% from last period</span>
                    </div>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                    <CreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">New Customers</p>
                    <h3 className="text-2xl font-bold dark:text-white">342</h3>
                    <div className="flex items-center mt-1 text-red-600 text-xs">
                      <TrendingDown className="h-3 w-3 mr-1" />
                      <span>2.5% from last period</span>
                    </div>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="overview" className="mb-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="products">Products</TabsTrigger>
              <TabsTrigger value="channels">Channels</TabsTrigger>
              <TabsTrigger value="geography">Geography</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="md:col-span-2 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base text-teal-800 dark:text-teal-300">Revenue Trend</CardTitle>
                    <CardDescription>Monthly revenue over time</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4">
                    <LineChart
                      data={[
                        { name: "Jan", Revenue: 4200000, Target: 4000000 },
                        { name: "Feb", Revenue: 3800000, Target: 4000000 },
                        { name: "Mar", Revenue: 4100000, Target: 4100000 },
                        { name: "Apr", Revenue: 4300000, Target: 4200000 },
                        { name: "May", Revenue: 4500000, Target: 4300000 },
                        { name: "Jun", Revenue: 4700000, Target: 4400000 },
                        { name: "Jul", Revenue: 4200000, Target: 4500000 },
                        { name: "Aug", Revenue: 4600000, Target: 4600000 },
                        { name: "Sep", Revenue: 4900000, Target: 4700000 },
                        { name: "Oct", Revenue: 5100000, Target: 4800000 },
                        { name: "Nov", Revenue: 5300000, Target: 4900000 },
                        { name: "Dec", Revenue: 5500000, Target: 5000000 },
                      ]}
                      index="name"
                      categories={["Revenue", "Target"]}
                      colors={["#00cc00", "#0088ff"]}
                      valueFormatter={(value) => `₹${(value / 1000000).toFixed(1)}M`}
                      yAxisWidth={60}
                      className="h-80"
                    />
                  </CardContent>
                </Card>

                <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base text-teal-800 dark:text-teal-300">Revenue by Category</CardTitle>
                    <CardDescription>Distribution across categories</CardDescription>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="h-80 flex items-center justify-center">
                      <PieChart
                        data={[
                          { name: "Banking", value: 42 },
                          { name: "Investments", value: 28 },
                          { name: "Insurance", value: 18 },
                          { name: "Loans", value: 12 },
                        ]}
                        index="name"
                        category="value"
                        valueFormatter={(value) => `${value}%`}
                        colors={["#00cc00", "#0088ff", "#ffbb00", "#ff0000"]}
                        className="h-80"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="products">
              <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base text-teal-800 dark:text-teal-300">Product Performance</CardTitle>
                  <CardDescription>Revenue by product category</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <BarChart
                    data={[
                      { name: "Savings Accounts", value: 1200000 },
                      { name: "Credit Cards", value: 980000 },
                      { name: "Personal Loans", value: 750000 },
                      { name: "Mortgages", value: 620000 },
                      { name: "Investment Funds", value: 540000 },
                      { name: "Insurance", value: 320000 },
                    ]}
                    index="name"
                    categories={["value"]}
                    colors={["#00cc00"]}
                    valueFormatter={(value) => `₹${(value / 1000000).toFixed(1)}M`}
                    yAxisWidth={60}
                    className="h-80"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Other tabs content would go here */}
          </Tabs>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-base text-teal-800 dark:text-teal-300">Top Performing Products</CardTitle>
                <CardDescription>Products with highest revenue</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                          Product
                        </th>
                        <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                          Revenue
                        </th>
                        <th className="text-left py-3 px-4 text-sm font-medium text-gray-500 dark:text-gray-400">
                          Growth
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {[
                        { product: "Premium Savings Account", revenue: "₹1.2M", growth: "+15.3%" },
                        { product: "Platinum Credit Card", revenue: "₹980K", growth: "+12.1%" },
                        { product: "Home Loan Plus", revenue: "₹750K", growth: "+8.7%" },
                        { product: "Retirement Fund", revenue: "₹620K", growth: "+5.2%" },
                        { product: "Life Insurance Premium", revenue: "₹540K", growth: "+3.8%" },
                      ].map((item, index) => (
                        <tr key={index} className="border-b border-gray-200 dark:border-gray-700">
                          <td className="py-3 px-4 text-sm dark:text-gray-200">{item.product}</td>
                          <td className="py-3 px-4 text-sm dark:text-gray-200">{item.revenue}</td>
                          <td className="py-3 px-4 text-sm text-green-600 dark:text-green-400">{item.growth}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            <Card className="dark:bg-gray-800 border-gray-200 dark:border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-base text-teal-800 dark:text-teal-300">Revenue Forecast</CardTitle>
                <CardDescription>Projected revenue for next quarter</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <LineChart
                  data={[
                    { name: "Jan", Actual: 4200000, Forecast: 4200000 },
                    { name: "Feb", Actual: 3800000, Forecast: 3800000 },
                    { name: "Mar", Actual: 4100000, Forecast: 4100000 },
                    { name: "Apr", Actual: 4300000, Forecast: 4300000 },
                    { name: "May", Actual: 4500000, Forecast: 4500000 },
                    { name: "Jun", Actual: 4700000, Forecast: 4700000 },
                    { name: "Jul", Actual: null, Forecast: 4900000 },
                    { name: "Aug", Actual: null, Forecast: 5100000 },
                    { name: "Sep", Actual: null, Forecast: 5300000 },
                  ]}
                  index="name"
                  categories={["Actual", "Forecast"]}
                  colors={["#00cc00", "#0088ff"]}
                  valueFormatter={(value) => (value ? `₹${(value / 1000000).toFixed(1)}M` : "")}
                  yAxisWidth={60}
                  className="h-80"
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
