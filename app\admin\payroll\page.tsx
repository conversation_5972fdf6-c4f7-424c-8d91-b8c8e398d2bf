"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DollarSign, Plus, Edit, Trash2, Search, Filter, Download, Calculator, FileText } from "lucide-react"
import { toast } from "@/hooks/use-toast"

interface PayrollRecord {
  id: string
  employeeId: string
  employeeName: string
  position: string
  baseSalary: number
  overtime: number
  bonuses: number
  deductions: number
  netPay: number
  payPeriod: string
  status: "Draft" | "Pending" | "Processed"
  avatar?: string
}

const mockPayrollData: PayrollRecord[] = [
  {
    id: "1",
    employeeId: "EMP001",
    employeeName: "John Doe",
    position: "Senior Developer",
    baseSalary: 5000,
    overtime: 500,
    bonuses: 200,
    deductions: 800,
    netPay: 4900,
    payPeriod: "January 2024",
    status: "Processed",
    avatar: "/images/avatar.png",
  },
  {
    id: "2",
    employeeId: "EMP002",
    employeeName: "Jane Smith",
    position: "Project Manager",
    baseSalary: 6000,
    overtime: 300,
    bonuses: 500,
    deductions: 900,
    netPay: 5900,
    payPeriod: "January 2024",
    status: "Pending",
    avatar: "/images/avatar.png",
  },
  {
    id: "3",
    employeeId: "EMP003",
    employeeName: "Mike Johnson",
    position: "Designer",
    baseSalary: 4500,
    overtime: 200,
    bonuses: 100,
    deductions: 700,
    netPay: 4100,
    payPeriod: "January 2024",
    status: "Draft",
    avatar: "/images/avatar.png",
  },
]

export default function AdminPayrollPage() {
  const [payrollData, setPayrollData] = useState<PayrollRecord[]>(mockPayrollData)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingRecord, setEditingRecord] = useState<PayrollRecord | null>(null)
  const [formData, setFormData] = useState({
    employeeName: "",
    position: "",
    baseSalary: "",
    overtime: "",
    bonuses: "",
    deductions: "",
    payPeriod: "January 2024",
  })

  const filteredData = payrollData.filter((record) => {
    const matchesSearch =
      record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || record.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const calculateNetPay = () => {
    const base = Number.parseFloat(formData.baseSalary) || 0
    const overtime = Number.parseFloat(formData.overtime) || 0
    const bonuses = Number.parseFloat(formData.bonuses) || 0
    const deductions = Number.parseFloat(formData.deductions) || 0
    return base + overtime + bonuses - deductions
  }

  const handleCreatePayroll = () => {
    setEditingRecord(null)
    setFormData({
      employeeName: "",
      position: "",
      baseSalary: "",
      overtime: "",
      bonuses: "",
      deductions: "",
      payPeriod: "January 2024",
    })
    setDialogOpen(true)
  }

  const handleEditPayroll = (record: PayrollRecord) => {
    setEditingRecord(record)
    setFormData({
      employeeName: record.employeeName,
      position: record.position,
      baseSalary: record.baseSalary.toString(),
      overtime: record.overtime.toString(),
      bonuses: record.bonuses.toString(),
      deductions: record.deductions.toString(),
      payPeriod: record.payPeriod,
    })
    setDialogOpen(true)
  }

  const handleSavePayroll = () => {
    const netPay = calculateNetPay()

    if (editingRecord) {
      // Update existing record
      setPayrollData((prev) =>
        prev.map((record) =>
          record.id === editingRecord.id
            ? {
                ...record,
                employeeName: formData.employeeName,
                position: formData.position,
                baseSalary: Number.parseFloat(formData.baseSalary) || 0,
                overtime: Number.parseFloat(formData.overtime) || 0,
                bonuses: Number.parseFloat(formData.bonuses) || 0,
                deductions: Number.parseFloat(formData.deductions) || 0,
                netPay,
                payPeriod: formData.payPeriod,
              }
            : record,
        ),
      )
      toast({
        title: "Payroll Updated",
        description: "Payroll record has been successfully updated",
      })
    } else {
      // Create new record
      const newRecord: PayrollRecord = {
        id: Date.now().toString(),
        employeeId: `EMP${String(payrollData.length + 1).padStart(3, "0")}`,
        employeeName: formData.employeeName,
        position: formData.position,
        baseSalary: Number.parseFloat(formData.baseSalary) || 0,
        overtime: Number.parseFloat(formData.overtime) || 0,
        bonuses: Number.parseFloat(formData.bonuses) || 0,
        deductions: Number.parseFloat(formData.deductions) || 0,
        netPay,
        payPeriod: formData.payPeriod,
        status: "Draft",
        avatar: "/images/avatar.png",
      }
      setPayrollData((prev) => [...prev, newRecord])
      toast({
        title: "Payroll Created",
        description: "New payroll record has been successfully created",
      })
    }

    setDialogOpen(false)
  }

  const handleDeletePayroll = (id: string) => {
    setPayrollData((prev) => prev.filter((record) => record.id !== id))
    toast({
      title: "Payroll Deleted",
      description: "Payroll record has been successfully deleted",
    })
  }

  const handleStatusChange = (id: string, newStatus: "Draft" | "Pending" | "Processed") => {
    setPayrollData((prev) => prev.map((record) => (record.id === id ? { ...record, status: newStatus } : record)))
    toast({
      title: "Status Updated",
      description: `Payroll status changed to ${newStatus}`,
    })
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      Draft: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
      Pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      Processed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    }
    return variants[status as keyof typeof variants] || variants.Draft
  }

  const stats = {
    totalPayroll: payrollData.reduce((sum, record) => sum + record.netPay, 0),
    processed: payrollData.filter((r) => r.status === "Processed").length,
    pending: payrollData.filter((r) => r.status === "Pending").length,
    draft: payrollData.filter((r) => r.status === "Draft").length,
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Payroll Management</h1>
          <p className="text-muted-foreground">Manage employee payroll, salaries, and compensation</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={handleCreatePayroll}>
            <Plus className="mr-2 h-4 w-4" />
            Create Payroll
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Payroll</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stats.totalPayroll.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processed</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.processed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Calculator className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <Edit className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.draft}</div>
          </CardContent>
        </Card>
      </div>

      {/* Payroll Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payroll Records</CardTitle>
          <CardDescription>Manage employee payroll and compensation details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Processed">Processed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Position</TableHead>
                <TableHead>Base Salary</TableHead>
                <TableHead>Overtime</TableHead>
                <TableHead>Bonuses</TableHead>
                <TableHead>Deductions</TableHead>
                <TableHead>Net Pay</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={record.avatar || "/placeholder.svg"} />
                      <AvatarFallback>{record.employeeName.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{record.employeeName}</div>
                      <div className="text-sm text-muted-foreground">{record.employeeId}</div>
                    </div>
                  </TableCell>
                  <TableCell>{record.position}</TableCell>
                  <TableCell>${record.baseSalary.toLocaleString()}</TableCell>
                  <TableCell>${record.overtime.toLocaleString()}</TableCell>
                  <TableCell>${record.bonuses.toLocaleString()}</TableCell>
                  <TableCell>${record.deductions.toLocaleString()}</TableCell>
                  <TableCell className="font-semibold">${record.netPay.toLocaleString()}</TableCell>
                  <TableCell>
                    <Select
                      value={record.status}
                      onValueChange={(value: "Draft" | "Pending" | "Processed") => handleStatusChange(record.id, value)}
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Draft">Draft</SelectItem>
                        <SelectItem value="Pending">Pending</SelectItem>
                        <SelectItem value="Processed">Processed</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline" onClick={() => handleEditPayroll(record)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeletePayroll(record.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Create/Edit Payroll Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{editingRecord ? "Edit Payroll" : "Create New Payroll"}</DialogTitle>
            <DialogDescription>
              {editingRecord ? "Update payroll information for the employee" : "Enter payroll details for the employee"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employeeName">Employee Name</Label>
                <Input
                  id="employeeName"
                  value={formData.employeeName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, employeeName: e.target.value }))}
                  placeholder="Enter employee name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => setFormData((prev) => ({ ...prev, position: e.target.value }))}
                  placeholder="Enter position"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="baseSalary">Base Salary ($)</Label>
                <Input
                  id="baseSalary"
                  type="number"
                  value={formData.baseSalary}
                  onChange={(e) => setFormData((prev) => ({ ...prev, baseSalary: e.target.value }))}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="overtime">Overtime ($)</Label>
                <Input
                  id="overtime"
                  type="number"
                  value={formData.overtime}
                  onChange={(e) => setFormData((prev) => ({ ...prev, overtime: e.target.value }))}
                  placeholder="0"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bonuses">Bonuses ($)</Label>
                <Input
                  id="bonuses"
                  type="number"
                  value={formData.bonuses}
                  onChange={(e) => setFormData((prev) => ({ ...prev, bonuses: e.target.value }))}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="deductions">Deductions ($)</Label>
                <Input
                  id="deductions"
                  type="number"
                  value={formData.deductions}
                  onChange={(e) => setFormData((prev) => ({ ...prev, deductions: e.target.value }))}
                  placeholder="0"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="payPeriod">Pay Period</Label>
              <Select
                value={formData.payPeriod}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, payPeriod: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="January 2024">January 2024</SelectItem>
                  <SelectItem value="February 2024">February 2024</SelectItem>
                  <SelectItem value="March 2024">March 2024</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium">Net Pay:</span>
                <span className="text-2xl font-bold text-green-600">${calculateNetPay().toLocaleString()}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSavePayroll}>{editingRecord ? "Update Payroll" : "Create Payroll"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
