"use client"
import { KanbanColumn } from "@/components/kanban-column"

interface Task {
  id: string
  title: string
  description: string
  status: string
  userId: string
  priority?: string
}

interface KanbanBoardProps {
  tasks?: Task[] | null
  onEditTask?: (task: Task) => void
  onDeleteTask?: (taskId: string) => void
  onUpdateTaskStatus?: (taskId: string, newStatus: string) => void
  isAdmin?: boolean
  currentUserId?: string
}

export function KanbanBoard({
  tasks = [],
  onEditTask = () => {},
  onDeleteTask = () => {},
  onUpdateTaskStatus = () => {},
  isAdmin = false,
  currentUserId = "",
}: KanbanBoardProps) {
  // Ensure we always have a valid tasks array
  const tasksArray = Array.isArray(tasks) ? tasks : []

  // Filter tasks by status
  const todoTasks = tasksArray.filter((task) => task.status === "todo")
  const inProgressTasks = tasksArray.filter((task) => task.status === "inprogress")
  const doneTasks = tasksArray.filter((task) => task.status === "done")

  // If there are no tasks at all, show a message
  if (tasksArray.length === 0) {
    return (
      <div className="flex items-center justify-center h-40 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/5 p-4 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">No tasks available. Create a new task to get started.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/5 p-4 pb-2 rounded-lg">
      <KanbanColumn
        title="To Do"
        tasks={todoTasks}
        onEditTask={onEditTask}
        onDeleteTask={onDeleteTask}
        onUpdateTaskStatus={onUpdateTaskStatus}
        isAdmin={isAdmin}
        currentUserId={currentUserId}
        columnId="todo"
      />
      <KanbanColumn
        title="In Progress"
        tasks={inProgressTasks}
        onEditTask={onEditTask}
        onDeleteTask={onDeleteTask}
        onUpdateTaskStatus={onUpdateTaskStatus}
        isAdmin={isAdmin}
        currentUserId={currentUserId}
        columnId="inprogress"
      />
      <KanbanColumn
        title="Done"
        tasks={doneTasks}
        onEditTask={onEditTask}
        onDeleteTask={onDeleteTask}
        onUpdateTaskStatus={onUpdateTaskStatus}
        isAdmin={isAdmin}
        currentUserId={currentUserId}
        columnId="done"
      />
    </div>
  )
}
