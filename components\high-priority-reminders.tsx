"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON>Triangle, Clock } from "lucide-react"

interface Task {
  id: string
  title: string
  description: string
  status: string
  userId: string
  priority?: string
}

interface HighPriorityRemindersProps {
  tasks?: Task[]
  onTaskClick?: (taskId: string) => void
}

export function HighPriorityReminders({ tasks = [], onTaskClick = () => {} }: HighPriorityRemindersProps) {
  // Ensure tasks is an array before filtering
  const tasksArray = Array.isArray(tasks) ? tasks : []

  // Filter high priority tasks
  const highPriorityTasks = tasksArray.filter((task) => task.priority === "high")

  if (highPriorityTasks.length === 0) {
    return null
  }

  return (
    <Card className="mb-4 border-exobank-red/20 bg-red-50 dark:bg-red-900/10 dark:border-red-900/30">
      <CardHeader className="py-2 px-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2 text-exobank-red dark:text-red-300">
            <AlertTriangle className="h-4 w-4" />
            High Priority Reminders
          </CardTitle>
          <Badge className="bg-exobank-red text-white">{highPriorityTasks.length}</Badge>
        </div>
      </CardHeader>
      <CardContent className="py-0 px-4 pb-2">
        <div className="space-y-2">
          {highPriorityTasks.slice(0, 3).map((task) => (
            <div
              key={task.id}
              className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded-md border border-red-100 dark:border-red-900/30 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
              onClick={() => onTaskClick(task.id)}
            >
              <div className="flex items-center gap-2">
                <Bell className="h-3 w-3 text-exobank-red dark:text-red-400" />
                <span className="text-sm font-medium dark:text-gray-200">{task.title}</span>
              </div>
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                <Clock className="h-3 w-3 mr-1" />
                <span>Today</span>
              </div>
            </div>
          ))}

          {highPriorityTasks.length > 3 && (
            <div className="text-center text-xs text-exobank-red dark:text-red-400 pt-1">
              +{highPriorityTasks.length - 3} more high priority tasks
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
