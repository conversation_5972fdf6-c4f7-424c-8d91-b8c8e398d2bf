"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, BarChart3, Calculator, Users, Settings } from "lucide-react"

export function BottomNavigation() {
  const pathname = usePathname()

  // Don't show bottom navigation on auth pages
  if (pathname?.startsWith("/auth/")) {
    return null
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t border-border md:hidden">
      <div className="flex justify-around items-center h-16">
        <Link
          href="/"
          className={`flex flex-col items-center justify-center w-full h-full text-xs ${
            pathname === "/" ? "text-primary" : "text-muted-foreground"
          }`}
        >
          <Home className="h-5 w-5 mb-1" />
          <span>Home</span>
        </Link>
        <Link
          href="/analytics"
          className={`flex flex-col items-center justify-center w-full h-full text-xs ${
            pathname === "/analytics" ? "text-primary" : "text-muted-foreground"
          }`}
        >
          <BarChart3 className="h-5 w-5 mb-1" />
          <span>Analytics</span>
        </Link>
        <Link
          href="/calculator"
          className={`flex flex-col items-center justify-center w-full h-full text-xs ${
            pathname === "/calculator" ? "text-primary" : "text-muted-foreground"
          }`}
        >
          <Calculator className="h-5 w-5 mb-1" />
          <span>Calculator</span>
        </Link>
        <Link
          href="/leads"
          className={`flex flex-col items-center justify-center w-full h-full text-xs ${
            pathname === "/leads" ? "text-primary" : "text-muted-foreground"
          }`}
        >
          <Users className="h-5 w-5 mb-1" />
          <span>Leads</span>
        </Link>
        <Link
          href="/settings"
          className={`flex flex-col items-center justify-center w-full h-full text-xs ${
            pathname === "/settings" ? "text-primary" : "text-muted-foreground"
          }`}
        >
          <Settings className="h-5 w-5 mb-1" />
          <span>Settings</span>
        </Link>
      </div>
    </div>
  )
}
