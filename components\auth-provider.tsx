"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

// Client-safe types (no database imports)
export type UserRole = "admin" | "hr_manager" | "manager" | "staff"

export interface User {
  id: string
  email: string
  full_name: string
  role: UserRole
  department?: string
  position?: string
  phone?: string
  hire_date?: string
  salary?: number
  is_active: boolean
  email_verified: boolean
  last_login?: string
  created_at: string
  updated_at: string
}

export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
  created_at: string
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  userPermissions: Permission[]
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ success: boolean; error?: string }>
  hasPermission: (permissionName: string) => boolean
  hasRole: (roles: UserRole | UserRole[]) => boolean
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [userPermissions, setUserPermissions] = useState<Permission[]>([])

  // Fetch user permissions based on role
  const fetchUserPermissions = async (role: UserRole) => {
    try {
      // For now, set empty permissions - can be implemented later with API endpoint
      setUserPermissions([])
    } catch (error) {
      console.error("Error fetching permissions:", error)
      setUserPermissions([])
    }
  }

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const response = await fetch("/api/auth/me", {
          credentials: "include",
          headers: {
            "Cache-Control": "no-cache",
          },
        })

        if (response.ok) {
          const userData = await response.json()
          if (userData && userData.id) {
            setUser(userData)
            if (userData.role) {
              await fetchUserPermissions(userData.role)
            }
          }
        }
      } catch (error) {
        console.error("Error initializing auth:", error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true)

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: email.trim(), password }),
        credentials: "include",
      })

      const data = await response.json()

      if (data.success && data.user) {
        setUser(data.user)
        if (data.user.role) {
          await fetchUserPermissions(data.user.role)
        }
        return { success: true }
      } else {
        return { success: false, error: data.error || "Login failed" }
      }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, error: "An unexpected error occurred" }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include",
      })

      setUser(null)
      setUserPermissions([])

      // Redirect to login page
      window.location.href = "/auth/login"
    } catch (error) {
      console.error("Error during logout:", error)
      // Force redirect even if logout API fails
      setUser(null)
      setUserPermissions([])
      window.location.href = "/auth/login"
    }
  }

  const signUp = async (email: string, password: string, userData: Partial<User>) => {
    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: email.trim(),
          password,
          full_name: userData.full_name || "",
          role: userData.role || "staff",
          department: userData.department,
          position: userData.position,
        }),
        credentials: "include",
      })

      const data = await response.json()

      if (data.success) {
        return { success: true }
      } else {
        return { success: false, error: data.error || "Registration failed" }
      }
    } catch (error) {
      console.error("Sign up error:", error)
      return { success: false, error: "An unexpected error occurred" }
    }
  }

  const hasPermission = (permissionName: string): boolean => {
    if (!user || !permissionName) return false
    return userPermissions.some((permission) => permission.name === permissionName)
  }

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!user) return false
    const roleArray = Array.isArray(roles) ? roles : [roles]
    return roleArray.includes(user.role)
  }

  const refreshUser = async () => {
    try {
      const response = await fetch("/api/auth/me", {
        credentials: "include",
        headers: {
          "Cache-Control": "no-cache",
        },
      })

      if (response.ok) {
        const userData = await response.json()
        if (userData && userData.id) {
          setUser(userData)
          if (userData.role) {
            await fetchUserPermissions(userData.role)
          }
        }
      }
    } catch (error) {
      console.error("Error refreshing user:", error)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        userPermissions,
        login,
        logout,
        signUp,
        hasPermission,
        hasRole,
        refreshUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
