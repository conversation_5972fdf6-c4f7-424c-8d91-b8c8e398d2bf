"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"

interface User {
  id: string
  name: string
  avatar?: string
}

interface UserSelectorProps {
  users: User[]
  selectedUser: string | null
  onSelectUser: (userId: string | null) => void
  className?: string
}

export function UserSelector({ users, selectedUser, onSelectUser, className = "" }: UserSelectorProps) {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Label htmlFor="user-filter" className="text-sm whitespace-nowrap text-teal-700">
        Team Member:
      </Label>
      <Select value={selectedUser || "all"} onValueChange={(value) => onSelectUser(value === "all" ? null : value)}>
        <SelectTrigger id="user-filter" className="w-[140px]">
          <SelectValue placeholder="All Users" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Users</SelectItem>
          {users.map((user) => (
            <SelectItem key={user.id} value={user.id}>
              {user.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
