"use client"

import type React from "react"

import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Edit, Trash2 } from "lucide-react"

interface Task {
  id: string
  title: string
  description: string
  status: string
  userId: string
  priority?: string
}

interface TaskCardProps {
  task: Task
  onEdit: () => void
  onDelete: () => void
  isAdmin?: boolean
  isOwnedByCurrentUser?: boolean
}

export function TaskCard({ task, onEdit, onDelete, isAdmin = false, isOwnedByCurrentUser = false }: TaskCardProps) {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("taskId", task.id)
  }

  // Determine if the current user can edit/delete this task
  const canModify = isAdmin || isOwnedByCurrentUser

  // Priority colors
  const priorityColors = {
    high: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
    medium: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
    low: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  }

  const priorityColor = task.priority
    ? priorityColors[task.priority as keyof typeof priorityColors]
    : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"

  const borderColor = {
    high: "border-l-red-500",
    medium: "border-l-orange-500",
    low: "border-l-blue-500",
  }

  const taskBorder = task.priority ? borderColor[task.priority as keyof typeof borderColor] : "border-l-gray-300"

  return (
    <Card
      className={`cursor-grab bg-white dark:bg-gray-700 shadow-sm border-l-4 ${taskBorder}`}
      draggable
      onDragStart={handleDragStart}
    >
      <CardContent className="p-3">
        <div className="flex justify-between items-start">
          <h4 className="font-medium text-sm mb-1 dark:text-white">{task.title}</h4>
          {task.priority && <Badge className={`text-xs ${priorityColor}`}>{task.priority}</Badge>}
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">{task.description}</p>
        {canModify && (
          <div className="flex justify-end gap-1 mt-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 dark:hover:text-green-400"
              onClick={(e) => {
                e.stopPropagation()
                onEdit()
              }}
            >
              <Edit className="h-3.5 w-3.5" />
              <span className="sr-only">Edit</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 dark:hover:text-red-400"
              onClick={(e) => {
                e.stopPropagation()
                onDelete()
              }}
            >
              <Trash2 className="h-3.5 w-3.5" />
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
